{"buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "framework": "nextjs", "functions": {"frontend/src/app/api/**/*.ts": {"runtime": "nodejs18.x", "maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-API-Key"}]}], "rewrites": [{"source": "/api/copilotkit/:path*", "destination": "/api/copilotkit/:path*"}]}