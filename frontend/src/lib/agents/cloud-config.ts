/**
 * LangGraph Cloud Agent Configuration
 *
 * This file defines the agent configurations for LangGraph Cloud deployment.
 * Each agent is configured with its cloud endpoint and capabilities.
 */

import { RemoteGraph } from "@langchain/langgraph/remote";

export interface CloudAgentConfig {
  id: string;
  name: string;
  description: string;
  graphId: string;
  capabilities: string[];
  maxTokens?: number;
  temperature?: number;
}

export interface LangGraphCloudConfig {
  apiKey: string;
  url: string;
}

/**
 * Cloud agent configurations for LangGraph Cloud deployment
 */
// LangGraph Cloud configuration
const LANGGRAPH_CLOUD_CONFIG: LangGraphCloudConfig = {
  apiKey: process.env.LANGGRAPH_API_KEY || '',
  url: process.env.LANGGRAPH_URL || 'https://api.langgraph.cloud'
};

export const CLOUD_AGENTS: Record<string, CloudAgentConfig> = {
  supervisor_agent: {
    id: 'supervisor-agent-v1',
    name: 'Supervisor Agent',
    description: 'Main routing and coordination agent for the AiLex platform',
    graphId: process.env.LANGGRAPH_SUPERVISOR_GRAPH_ID || 'supervisor-agent',
    capabilities: [
      'intent_routing',
      'task_coordination',
      'agent_supervision',
      'workflow_management'
    ],
    maxTokens: 4000,
    temperature: 0.2
  },
  
  intake_agent: {
    id: process.env.CLOUD_AGENT_ID_INTAKE || 'intake-agent-v1',
    name: 'Intake Agent',
    description: 'Legal intake specialist for personal injury cases',
    cloudEndpoint: 'https://api.langgraph.cloud/v1/agents/intake',
    capabilities: [
      'client_intake',
      'case_assessment',
      'form_completion',
      'document_collection'
    ],
    maxTokens: 6000,
    temperature: 0.3
  },
  
  research_agent: {
    id: process.env.CLOUD_AGENT_ID_RESEARCH || 'research-agent-v1',
    name: 'Research Agent',
    description: 'Legal research specialist with access to case law and statutes',
    cloudEndpoint: 'https://api.langgraph.cloud/v1/agents/research',
    capabilities: [
      'legal_research',
      'case_law_analysis',
      'statute_lookup',
      'precedent_analysis'
    ],
    maxTokens: 8000,
    temperature: 0.1
  },
  
  document_agent: {
    id: process.env.CLOUD_AGENT_ID_DOCUMENT || 'document-agent-v1',
    name: 'Document Agent',
    description: 'Document drafting and analysis specialist',
    cloudEndpoint: 'https://api.langgraph.cloud/v1/agents/document',
    capabilities: [
      'document_drafting',
      'template_completion',
      'document_analysis',
      'content_generation'
    ],
    maxTokens: 10000,
    temperature: 0.2
  },
  
  deadline_agent: {
    id: process.env.CLOUD_AGENT_ID_DEADLINE || 'deadline-agent-v1',
    name: 'Deadline Agent',
    description: 'Deadline extraction and calendar management specialist',
    cloudEndpoint: 'https://api.langgraph.cloud/v1/agents/deadline',
    capabilities: [
      'deadline_extraction',
      'calendar_management',
      'reminder_scheduling',
      'compliance_tracking'
    ],
    maxTokens: 4000,
    temperature: 0.1
  },
  
  event_agent: {
    id: process.env.CLOUD_AGENT_ID_EVENT || 'event-agent-v1',
    name: 'Event Agent',
    description: 'Calendar event creation and management specialist',
    cloudEndpoint: 'https://api.langgraph.cloud/v1/agents/event',
    capabilities: [
      'event_creation',
      'calendar_integration',
      'scheduling_optimization',
      'conflict_resolution'
    ],
    maxTokens: 4000,
    temperature: 0.2
  }
};

/**
 * Get agent configuration by name
 */
export function getAgentConfig(agentName: string): CloudAgentConfig | null {
  return CLOUD_AGENTS[agentName] || null;
}

/**
 * Get all available agent names
 */
export function getAvailableAgents(): string[] {
  return Object.keys(CLOUD_AGENTS);
}

/**
 * Create a RemoteGraph instance for a specific agent
 */
export function createRemoteGraph(agentName: string): RemoteGraph | null {
  const config = getAgentConfig(agentName);
  if (!config) {
    return null;
  }

  return new RemoteGraph({
    graphId: config.graphId,
    apiKey: LANGGRAPH_CLOUD_CONFIG.apiKey,
    url: LANGGRAPH_CLOUD_CONFIG.url,
  });
}

/**
 * Validate agent configuration
 */
export function validateAgentConfig(config: CloudAgentConfig): boolean {
  return !!(
    config.id &&
    config.name &&
    config.description &&
    config.graphId &&
    config.capabilities &&
    config.capabilities.length > 0
  );
}
